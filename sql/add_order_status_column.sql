-- 为tbl_order表添加Status字段的迁移脚本
-- 执行日期: 2024年

-- 添加Status列
ALTER TABLE `tbl_order` 
ADD COLUMN `Status` varchar(20) NOT NULL DEFAULT 'New' COMMENT '订单状态' 
AFTER `Kafka_Status`;

-- 为现有数据设置默认状态
UPDATE `tbl_order` SET `Status` = 'New' WHERE `Status` IS NULL OR `Status` = '';

-- 添加索引以提高查询性能
CREATE INDEX `idx_order_status` ON `tbl_order` (`Status`);

-- 验证更改
SELECT COUNT(*) as total_orders, Status, COUNT(*) as count_by_status 
FROM `tbl_order` 
GROUP BY Status;
