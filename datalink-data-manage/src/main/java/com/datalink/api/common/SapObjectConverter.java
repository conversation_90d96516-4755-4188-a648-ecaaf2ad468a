package com.datalink.api.common;

import com.datalink.api.domain.SapAsnItem;
import com.datalink.api.domain.SapGrItem;
import com.datalink.api.domain.SapListRequst;
import com.datalink.api.domain.SapPoItem;
import com.datalink.api.domain.SapEkpoItem;
import com.datalink.api.domain.SapEketItem;
import com.datalink.api.domain.SapNewOrderRequest;
import com.datalink.api.domain.dto.TblForecastItemDTO;
import com.datalink.common.DataConstants;
import com.datalink.common.core.domain.entity.SysDictData;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.system.service.ISysDictDataService;
import com.datalink.system.service.ISysDictTypeService;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class SapObjectConverter {

    /**
     * 将DTO转换为TblForecast对象
     * 注意：传入的dtoList应该已经按照ZFNO分组，所有元素具有相同的预测编号
     *
     * @param dtoList 已按ZFNO分组的DTO列表
     * @return TblForecast对象
     */
    public static TblForecast convertToTblForecast(List<TblForecastItemDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }

        // 由于已经按照ZFNO分组，所有元素具有相同的预测编号，使用第一个元素获取共同属性
        TblForecastItemDTO firstItem = dtoList.get(0);

        TblForecast forecast = new TblForecast();
        forecast.setForecastCode(firstItem.getForecastCode());
        forecast.setPlantCode(firstItem.getPlantCode());
        forecast.setSuppCode(firstItem.getSuppCode());

        // 设置行项目列表
        List<TblForecastItem> itemList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        for (TblForecastItemDTO dto : dtoList) {
            TblForecastItem item = new TblForecastItem();
            item.setItemNo(dto.getItemNo());
            item.setArticleNo(dto.getArticleNo());

            // 设置交货日期，从KMONTH解析
            try {
                if (StringUtils.isNotEmpty(dto.getKmonth())) {
                    item.setDeliveryDate(sdf.parse(dto.getKmonth()));
                }
            } catch (ParseException e) {
                throw new RuntimeException("解析预测日期失败: " + dto.getKmonth(), e);
            }

            // 设置数量
            if (StringUtils.isNotEmpty(dto.getQuantity())) {
                try {
                    item.setQuantity(Long.parseLong(dto.getQuantity()));
                } catch (NumberFormatException e) {
                    throw new RuntimeException("解析预测数量失败: " + dto.getQuantity(), e);
                }
            }

            item.setUnit(dto.getUnit());
            item.setProType(dto.getProType());

            itemList.add(item);
        }

        forecast.setDetail(itemList);
        return forecast;
    }
    public static List<TblFeedback> convertToFeedBack(SapListRequst<SapGrItem> request){
        Map<String, TblFeedback> feedbackMap = new HashMap<String, TblFeedback>();
        for (SapGrItem item : request.getItemList()){
            String asnNo = item.getLIFEX();
            TblFeedback feedback = feedbackMap.get(asnNo);
            if (feedback == null){
                feedback = new TblFeedback();
                feedback.setDnNo(asnNo);
                feedback.setDepot(item.getZDEPOT());
                feedback.setCompCode(item.getBUKRS());
                feedback.setPlantCode(item.getWERKS());
                feedback.setReceivingPlace(item.getWANST());
                feedback.setReceivingQuantity(convertSapNumber(item.getMENGE()));
                feedback.setPlantName("");
                feedback.setSuppCode(item.getLIFNR());
                feedback.setSuppName("");
                feedback.setOrderUnit(item.getMEINS());
                feedback.setReceivingDate(convertSapDate(item.getEINDT()));
                feedback.setDeliveryNoteDate(convertSapDate(item.getERDAT()));
                feedback.setDeliveryNoteTime(convertSapTime(item.getERZET()));
                feedback.setDirection(DataConstants.DIRECTION_IN);
                feedback.setKafkaStatus(DataConstants.KAFKA_STATUS_NO_SENT);
                feedback.setDetail(new ArrayList<TblFeedbackItem>());
                feedbackMap.put(asnNo, feedback);
            }
            TblFeedbackItem feedbackItem = new TblFeedbackItem();
            feedbackItem.setArticleNo(item.getMATNR());
            feedbackItem.setArticleName(item.getMAKTX());
            feedbackItem.setQuantity(convertSapNumber(item.getMENGE_1()));
            feedbackItem.setOrderCode(item.getEBELN());
            feedbackItem.setOrderLineNo(item.getEBELP());
            feedbackItem.setUnit(item.getMEINS_1());
            feedbackItem.setRcvDate(convertSapDate(item.getBUDAT()));
            feedbackItem.setRcvTime(DateUtils.parseDate("1970-01-01"));
            feedbackItem.setRcvDocNo(item.getMBLNR());
            feedbackItem.setRcvDocItemNo(convertSapNumber(item.getZEILE()).intValue());
            feedback.getDetail().add(feedbackItem);
        }
        return new ArrayList<>(feedbackMap.values());
    }

    public static List<TblOrder> convertToTblOrder(SapListRequst<SapPoItem> request){
        Map<String, TblOrder> poMap = new LinkedHashMap<>();  // 使用LinkedHashMap保持插入顺序
        for (SapPoItem item : request.getItemList()){
            String poCode = item.getEBELN();
            TblOrder po = poMap.get(poCode);
            if (po == null){
                po = new TblOrder();
                po.setOrderCode(poCode);
                po.setDirection(DataConstants.DIRECTION_IN);
                po.setCompCode(item.getBUKRS());
                po.setCompName(item.getZNAME());
                po.setPlantCode(item.getWERKS());
//                po.setPlantName(item.getSORT1());
                po.setSuppCode(item.getLIFNR());
                po.setSuppName(item.getZNAME1());
                po.setPlannerNo(item.getEKORG());
                po.setDetail(new ArrayList<TblOrderItem>());
                // 若AEDAT长度为8，则表示是YYYYMMDD格式，需要补0成YYYYMMDDHHMMSS格式
                if (item.getAEDAT().length() == 8) {
                    item.setAEDAT(item.getAEDAT() + "000000");
                }
                po.setCreateTime(convertSapDateTime(item.getAEDAT()));
                if (item.getZMDDT() != null) {
                    po.setSapUpdateTime(convertSapDateTime(item.getZMDDT()));
                }
                po.setCustomerCode(item.getKUNNR());
                po.setRequester(item.getVBPA());
                poMap.put(poCode, po);
            }
            TblOrderItem orderItem = new TblOrderItem();
            orderItem.setArticleNo(item.getMATNR());
            orderItem.setArticleName(item.getTXZ01());
            orderItem.setItemNo(item.getEBELP());
            orderItem.setQuantity(convertSapNumber(item.getMENGE()));
            orderItem.setUnit(item.getMEINS());
            orderItem.setCurrencyCode(item.getWAERS());
            orderItem.setDeliveryDate(convertSapDate(item.getEINDT()));
            orderItem.setItemType(item.getPSTYP());
            orderItem.setDelIden(item.getLOKEZ());
            orderItem.setQtyPerPack(convertSapNumber(item.getBSTRF()));
            orderItem.setPriceUnit(item.getPEINH());
            orderItem.setRemark(item.getEDATU_02());
            orderItem.setPurDocType(item.getBSART());
            orderItem.setNetPrice(convertSapNumber(item.getNETPR()));
            orderItem.setStockLoc(item.getLGORT());
            orderItem.setLocAdd(item.getLGPBE());
            orderItem.setUnloadingNo(item.getWANST());
            orderItem.setUnloadingName(item.getSORT1());
            orderItem.setCustomerOrderCode(item.getBSTNK());
            orderItem.setCustomerOrderLineCode(item.getPOSNR());
            orderItem.setRcvType(item.getZMETHD());
            orderItem.setCustomerDeliveryDate(convertSapDateTime(item.getEDATU()));
            orderItem.setProductType(item.getSTRGR());
            orderItem.setPurchaseType(item.getZMM001());
            orderItem.setDepot(item.getZDEPOT());
            orderItem.setRcvName(item.getZMM002());
            orderItem.setCustomerArticleNo(item.getKDMAT());
            orderItem.setSecure(item.getMAABC());
            orderItem.setArticleType(item.getCODE());
            orderItem.setDeliverySplit(convertSapNumber(item.getZFGHS()).intValue());
            orderItem.setOrderType(item.getAUART());
            orderItem.setSoOrderQuantity(convertSapNumber(item.getKWMEMG()));
            orderItem.setCustomerPoNo(item.getBSTNK());
            orderItem.setRankNo(item.getIHREZ());
            orderItem.setBoxPackageQuantity(item.getZXBSL());
            orderItem.setSupplierCode03(item.getLIFNR_03());
            orderItem.setSupplierCode04(item.getLIFNR_04());
            orderItem.setPoRan(item.getZPORAN());
            orderItem.setSoCreateDate(convertSapDate(item.getERDAT()));
            orderItem.setShipmentPlant02(item.getWERKS_02());
            po.getDetail().add(orderItem);
        }
        return new ArrayList<>(poMap.values());
    }

    /**
     * 将新格式的SAP订单数据转换为TblOrder对象列表
     * @param request 新格式的SAP订单请求
     * @return TblOrder对象列表
     */
    public static List<TblOrder> convertNewFormatToTblOrder(SapOrderRequest request) {
        Map<String, TblOrder> orderMap = new LinkedHashMap<>();

        for (SapOrderItem item : request.getDATA()) {
            String orderCode = item.getZFNO();
            TblOrder order = orderMap.get(orderCode);

            if (order == null) {
                order = new TblOrder();
                order.setOrderCode(orderCode);
                order.setDirection(DataConstants.DIRECTION_IN);
                order.setStatus(DataConstants.ORDER_STATUS_NEW);
                order.setPlantCode(item.getWERKS());
                order.setSuppCode(item.getLIFNR());
                order.setDetail(new ArrayList<TblOrderItem>());
                orderMap.put(orderCode, order);
            }

            TblOrderItem orderItem = new TblOrderItem();
            orderItem.setItemNo(item.getZFPOS());
            orderItem.setArticleNo(item.getMATNR());
            orderItem.setUnit(item.getMEINS());
            orderItem.setItemType(item.getFCTYPE());

            // 解析数量
            if (StringUtils.isNotEmpty(item.getMENGE())) {
                try {
                    orderItem.setQuantity(convertSapNumber(item.getMENGE()));
                } catch (Exception e) {
                    throw new RuntimeException("解析订单数量失败: " + item.getMENGE(), e);
                }
            }

            // 解析交货日期，从KMONTH解析
            if (StringUtils.isNotEmpty(item.getKMONTH())) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    orderItem.setDeliveryDate(sdf.parse(item.getKMONTH()));
                } catch (ParseException e) {
                    throw new RuntimeException("解析交货日期失败: " + item.getKMONTH(), e);
                }
            }

            order.getDetail().add(orderItem);
        }

        return new ArrayList<>(orderMap.values());
    }

    public static SapListRequst<SapAsnItem> convertToSapAsn(TblAsn asn, List<TblOrder> orderList){
        SapListRequst<SapAsnItem> request = new SapListRequst<>();
        List<SapAsnItem> itemList = new ArrayList<>();
        for (TblAsnItem item : asn.getDetail()){
            for (TblAsnArticle article : item.getArticles()){
                SapAsnItem sapItem = new SapAsnItem();
//                for(TblOrder order : orderList){
//                    if (order.getOrderCode().equals(item.getOrderCode())){
//                        for (TblOrderItem orderItem : order.getDetail()){
//                            if (orderItem.getItemNo().equals(article.getOrderLineNo())){
//                                sapItem.setLFDAT(DateFormatUtils.format(orderItem.getDeliveryDate(), "yyyyMMdd"));
//                                break;
//                            }
//                        }
//                        break;
//                    }
//                }
                sapItem.setVERUR_LA(asn.getAsnCode());
                sapItem.setVGBEL(item.getOrderCode());
                sapItem.setVGPOS(article.getOrderLineNo());
                sapItem.setMATNR(article.getArticleNo());
                sapItem.setLFIMG(article.getQuantity());
                sapItem.setWADAT(DateFormatUtils.format(asn.getDeliveryDate(), "yyyyMMdd", TimeZone.getTimeZone("GMT+9")));
                sapItem.setLFDAT(DateFormatUtils.format(asn.getDeliveryDate(), "yyyyMMdd", TimeZone.getTimeZone("GMT+9")));
                sapItem.setLGORT(item.getRcvLocNo());
                sapItem.setVSTEL(item.getUnloadingNo());
                sapItem.setWERKS(item.getPlantCode());
                sapItem.setBSTRF(article.getPackQty().intValue());
                itemList.add(sapItem);
            }
        }
        request.setItemList(itemList);
        return request;
    }

    public static Map<String,Object> convertToMap(SapAsnItem item){
        Map<String,Object> map = new HashMap<>();
        map.put("LFDAT",item.getLFDAT());
        map.put("LFIMG",item.getLFIMG());
        map.put("LFUHR",item.getLFUHR());
        map.put("LGORT",item.getLGORT());
        map.put("MATNR",item.getMATNR());
        map.put("VERUR_LA",item.getVERUR_LA());
        map.put("VGBEL",item.getVGBEL());
        map.put("VGPOS",item.getVGPOS());
        map.put("VSTEL",item.getVSTEL());
        map.put("WADAT",item.getWADAT());
        map.put("WERKS",item.getWERKS());
        map.put("BSTRF",item.getBSTRF());
        return map;
    }

    public static BigDecimal convertSapNumber(String numberStr){
        String str = numberStr.trim();
        if (str.endsWith("-")){
            str = "-"+str.replace("-","");
        }else if(str.endsWith("+")){
            str = str.replace("+","");
        }
        return new BigDecimal(str);
    }

    public static Date convertSapDateTime(String dateStr){
        if(dateStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("yyyyMMddHHmmss", dateStr.trim());
    }

    public static Date convertSapDate(String dateStr){
        if(dateStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("yyyyMMdd", dateStr.trim());
    }

    public static Date convertSapTime(String timeStr){
        if(timeStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("HHmmss", timeStr.trim());
    }

    // 根据配置从部品番号中拆分制品区分字段
    public static String handleProductClass(ISysDictDataService dictDataService, String partNumber){
        if (StringUtils.isNotEmpty(dictDataService.selectDictLabel("split_part_number", partNumber))) {
            return partNumber.substring(partNumber.indexOf("+") + 1);
        }
//        List<SysDictData> splitPartNumbers = dictTypeService.selectDictDataByType("split_part_number");
//        if (splitPartNumbers.stream().anyMatch(item -> partNumber.equals(item.getDictValue()))) {
//            return partNumber.substring(partNumber.indexOf("+") + 1);
//        }
        return "";
    }
}
