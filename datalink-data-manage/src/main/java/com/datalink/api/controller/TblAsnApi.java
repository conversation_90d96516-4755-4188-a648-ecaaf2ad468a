package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.service.ITblAsnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("ASN接口")
@Validated
@RestController
@RequestMapping("/api/asn")
public class TblAsnApi {
    @Autowired
    private ITblAsnService asnService;

    //    @ApiOperation("查询ASN")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"asnid","searchvalue","createby","updateby","updatetime","params","createtime","itemid","direction","kafkastatus","articleid","docno","docdate","remark"}, value={TblAsn.class, TblAsnItem.class, TblAsnArticle.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询ASN", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblAsn> asnList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblAsn searchParam = new TblAsn();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            asnList = asnService.selectTblAsnFullList(searchParam);
        }
        if(asnList.isEmpty()){
            Long lastId = asnService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+asnList.get(asnList.size()-1).getAsnId());
            ajax.put("time", asnList.get(asnList.size()-1).getCreateTime());
        }

        ajax.put("items", asnList);
        return ajax;
    }
}
