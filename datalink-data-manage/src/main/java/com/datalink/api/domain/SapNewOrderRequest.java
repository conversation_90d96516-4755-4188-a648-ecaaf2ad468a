package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SAP新格式订单请求DTO
 */
public class SapNewOrderRequest {

    /** 采购订单号 */
    @JsonProperty("EBELN")
    @NotEmpty(message = "采购订单号不能为空")
    private String EBELN;

    /** 公司代码 */
    @JsonProperty("BUKRS")
    @NotEmpty(message = "公司代码不能为空")
    private String BUKRS;

    /** 采购凭证类型 */
    @JsonProperty("BSART")
    private String BSART;

    /** 删除标识 */
    @JsonProperty("LOEKZ")
    private String LOEKZ;

    /** 创建人 */
    @JsonProperty("ERNAM")
    private String ERNAM;

    /** 供应商编号 */
    @JsonProperty("LIFNR")
    @NotEmpty(message = "供应商编号不能为空")
    private String LIFNR;

    /** 货币 */
    @JsonProperty("WAERS")
    private String WAERS;

    /** 凭证日期 */
    @JsonProperty("BEDAT")
    private String BEDAT;

    /** 订单行项目 */
    @JsonProperty("EKPO")
    @Valid
    @NotNull(message = "订单行项目不能为空")
    private List<SapEkpoItem> EKPO;

    public String getEBELN() {
        return EBELN;
    }

    public void setEBELN(String EBELN) {
        this.EBELN = EBELN;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getBSART() {
        return BSART;
    }

    public void setBSART(String BSART) {
        this.BSART = BSART;
    }

    public String getLOEKZ() {
        return LOEKZ;
    }

    public void setLOEKZ(String LOEKZ) {
        this.LOEKZ = LOEKZ;
    }

    public String getERNAM() {
        return ERNAM;
    }

    public void setERNAM(String ERNAM) {
        this.ERNAM = ERNAM;
    }

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getWAERS() {
        return WAERS;
    }

    public void setWAERS(String WAERS) {
        this.WAERS = WAERS;
    }

    public String getBEDAT() {
        return BEDAT;
    }

    public void setBEDAT(String BEDAT) {
        this.BEDAT = BEDAT;
    }

    public List<SapEkpoItem> getEKPO() {
        return EKPO;
    }

    public void setEKPO(List<SapEkpoItem> EKPO) {
        this.EKPO = EKPO;
    }

    @Override
    public String toString() {
        return "SapNewOrderRequest{" +
                "EBELN='" + EBELN + '\'' +
                ", BUKRS='" + BUKRS + '\'' +
                ", BSART='" + BSART + '\'' +
                ", LOEKZ='" + LOEKZ + '\'' +
                ", ERNAM='" + ERNAM + '\'' +
                ", LIFNR='" + LIFNR + '\'' +
                ", WAERS='" + WAERS + '\'' +
                ", BEDAT='" + BEDAT + '\'' +
                ", EKPO=" + EKPO +
                '}';
    }
}
