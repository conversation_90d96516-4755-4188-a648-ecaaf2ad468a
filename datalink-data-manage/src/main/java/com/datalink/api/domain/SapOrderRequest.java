package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * SAP订单请求DTO
 * 用于接收新的订单JSON格式
 */
public class SapOrderRequest {

    /** 订单数据列表 */
    @JsonProperty("DATA")
    @Valid
    @NotEmpty(message = "订单数据不能为空")
    private List<SapOrderItem> DATA;

    public List<SapOrderItem> getDATA() {
        return DATA;
    }

    public void setDATA(List<SapOrderItem> DATA) {
        this.DATA = DATA;
    }

    @Override
    public String toString() {
        return "SapOrderRequest{" +
                "DATA=" + DATA +
                '}';
    }
}
