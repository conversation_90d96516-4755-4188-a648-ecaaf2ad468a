package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * SAP交货计划DTO
 */
public class SapEketItem {

    /** 交货计划行号 */
    @JsonProperty("ETENR")
    private String ETENR;

    /** 交货日期 */
    @JsonProperty("EINDT")
    private String EINDT;

    /** 交货数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    public String getETENR() {
        return ETENR;
    }

    public void setETENR(String ETENR) {
        this.ETENR = ETENR;
    }

    public String getEINDT() {
        return EINDT;
    }

    public void setEINDT(String EINDT) {
        this.EINDT = EINDT;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    @Override
    public String toString() {
        return "SapEketItem{" +
                "ETENR='" + ETENR + '\'' +
                ", EINDT='" + EINDT + '\'' +
                ", MENGE='" + MENGE + '\'' +
                '}';
    }
}
