package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import java.util.List;

/**
 * SAP订单行项目DTO
 */
public class SapEkpoItem {

    /** 采购订单行项目编号 */
    @JsonProperty("EBELP")
    private String EBELP;

    /** 删除标识 */
    @JsonProperty("LOEKZ")
    private String LOEKZ;

    /** 物料编号 */
    @JsonProperty("MATNR")
    private String MATNR;

    /** 物料描述 */
    @JsonProperty("TXZ01")
    private String TXZ01;

    /** 工厂 */
    @JsonProperty("WERKS")
    private String WERKS;

    /** 库存地点 */
    @JsonProperty("LGORT")
    private String LGORT;

    /** 订单数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    /** 订单单位 */
    @JsonProperty("MEINS")
    private String MEINS;

    /** 交货计划 */
    @JsonProperty("EKET")
    @Valid
    private List<SapEketItem> EKET;

    public String getEBELP() {
        return EBELP;
    }

    public void setEBELP(String EBELP) {
        this.EBELP = EBELP;
    }

    public String getLOEKZ() {
        return LOEKZ;
    }

    public void setLOEKZ(String LOEKZ) {
        this.LOEKZ = LOEKZ;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public String getTXZ01() {
        return TXZ01;
    }

    public void setTXZ01(String TXZ01) {
        this.TXZ01 = TXZ01;
    }

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getLGORT() {
        return LGORT;
    }

    public void setLGORT(String LGORT) {
        this.LGORT = LGORT;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }

    public List<SapEketItem> getEKET() {
        return EKET;
    }

    public void setEKET(List<SapEketItem> EKET) {
        this.EKET = EKET;
    }

    @Override
    public String toString() {
        return "SapEkpoItem{" +
                "EBELP='" + EBELP + '\'' +
                ", LOEKZ='" + LOEKZ + '\'' +
                ", MATNR='" + MATNR + '\'' +
                ", TXZ01='" + TXZ01 + '\'' +
                ", WERKS='" + WERKS + '\'' +
                ", LGORT='" + LGORT + '\'' +
                ", MENGE='" + MENGE + '\'' +
                ", MEINS='" + MEINS + '\'' +
                ", EKET=" + EKET +
                '}';
    }
}
