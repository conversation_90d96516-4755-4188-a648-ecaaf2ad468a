package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * SAP订单项目DTO
 * 用于接收新的订单JSON格式
 */
public class SapOrderItem {

    /** 订单编号 */
    @JsonProperty("ZFNO")
    private String ZFNO;

    /** 订单行项目编号 */
    @JsonProperty("ZFPOS")
    private String ZFPOS;

    /** 工厂代码 */
    @JsonProperty("WERKS")
    private String WERKS;

    /** 物料编号 */
    @JsonProperty("MATNR")
    private String MATNR;

    /** 供应商编号 */
    @JsonProperty("LIFNR")
    private String LIFNR;

    /** 预测类型 */
    @JsonProperty("FCTYPE")
    private String FCTYPE;

    /** 月份 */
    @JsonProperty("KMONTH")
    private String KMONTH;

    /** 数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    /** 单位 */
    @JsonProperty("MEINS")
    private String MEINS;

    public String getZFNO() {
        return ZFNO;
    }

    public void setZFNO(String ZFNO) {
        this.ZFNO = ZFNO;
    }

    public String getZFPOS() {
        return ZFPOS;
    }

    public void setZFPOS(String ZFPOS) {
        this.ZFPOS = ZFPOS;
    }

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getFCTYPE() {
        return FCTYPE;
    }

    public void setFCTYPE(String FCTYPE) {
        this.FCTYPE = FCTYPE;
    }

    public String getKMONTH() {
        return KMONTH;
    }

    public void setKMONTH(String KMONTH) {
        this.KMONTH = KMONTH;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }

    @Override
    public String toString() {
        return "SapOrderItem{" +
                "ZFNO='" + ZFNO + '\'' +
                ", ZFPOS='" + ZFPOS + '\'' +
                ", WERKS='" + WERKS + '\'' +
                ", MATNR='" + MATNR + '\'' +
                ", LIFNR='" + LIFNR + '\'' +
                ", FCTYPE='" + FCTYPE + '\'' +
                ", KMONTH='" + KMONTH + '\'' +
                ", MENGE='" + MENGE + '\'' +
                ", MEINS='" + MEINS + '\'' +
                '}';
    }
}
