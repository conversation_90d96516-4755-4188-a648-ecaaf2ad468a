# 批量接收订单接口调整说明

## 概述
根据提供的新JSON报文格式，对批量接收订单接口进行了调整，并新增了订单状态字段。

## 主要变更

### 1. 数据库结构变更
- **表名**: `tbl_order`
- **新增字段**: `Status` varchar(20) NOT NULL DEFAULT 'New' COMMENT '订单状态'
- **位置**: 在 `Kafka_Status` 字段之后
- **默认值**: 'New'

### 2. 新增常量定义
在 `DataConstants.java` 中新增：
```java
public static final String ORDER_STATUS_NEW = "New";
public static final String ORDER_STATUS_CONFIRMED = "Confirmed";
```

### 3. 实体类更新
- **TblOrder.java**: 新增 `status` 字段及其getter/setter方法

### 4. 新增DTO类
- **SapEketItem.java**: 处理交货计划数据
- **SapEkpoItem.java**: 处理订单行项目数据
- **SapNewOrderRequest.java**: 处理新JSON格式的请求包装类

### 5. 转换器更新
- **SapObjectConverter.java**: 新增 `convertNewFormatToTblOrder()` 方法处理新格式数据转换

### 6. 数据库映射更新
- **TblOrderMapper.xml**: 更新所有相关SQL语句以支持Status字段

### 7. 新增API接口
- **TblOrderApi.java**: 新增 `/api/order/batchReceive` 接口

## 新JSON格式说明

### 请求格式
```json
{
    "EBELN": "4500000030",     // 采购订单号
    "BUKRS": "H217",           // 公司代码
    "BSART": "ZNB1",           // 采购凭证类型
    "LOEKZ": "",               // 删除标识
    "ERNAM": "HMMM01",         // 创建人
    "LIFNR": "0000100003",     // 供应商编号
    "WAERS": "CNY",            // 货币
    "BEDAT": "20210827",       // 凭证日期
    "EKPO": [                  // 订单行项目
        {
            "EBELP": "00010",      // 行项目编号
            "LOEKZ": "",           // 删除标识
            "MATNR": "ZSD_TEST02", // 物料编号
            "TXZ01": "螺钉",        // 物料描述
            "WERKS": "2171",       // 工厂
            "LGORT": "1001",       // 库存地点
            "MENGE": "10000",      // 订单数量
            "MEINS": "PCS",        // 单位
            "EKET": [              // 交货计划
                {
                    "ETENR": "0001",     // 交货计划行号
                    "EINDT": "20211029", // 交货日期
                    "MENGE": "10000"     // 交货数量
                }
            ]
        }
    ]
}
```

### 字段映射关系

#### 订单头字段映射
| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| EBELN | Order_Code | 采购订单号 |
| BUKRS | Comp_Code | 公司代码 |
| BSART | Pur_Doc_Type | 采购凭证类型 |
| LOEKZ | Del_Iden | 删除标识 |
| LIFNR | Supp_Code | 供应商编号 |
| WAERS | Currency_Code | 货币 |
| BEDAT | Sap_Update_Time | 凭证日期 |

#### 订单行项目字段映射
| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| EBELP | Item_No | 行项目编号 |
| LOEKZ | Del_Iden | 删除标识 |
| MATNR | Article_No | 物料编号 |
| TXZ01 | Article_Name | 物料描述 |
| WERKS | Plant_Code | 工厂 |
| LGORT | Stock_Loc | 库存地点 |
| MENGE | Quantity | 订单数量 |
| MEINS | Unit | 单位 |

#### 交货计划字段映射
| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| ETENR | - | 交货计划行号(用于区分多个交货计划) |
| EINDT | Delivery_Date | 交货日期 |
| MENGE | Quantity | 交货数量 |

## API接口说明

### 新接口
- **URL**: `POST /api/order/batchReceive`
- **功能**: 批量接收新格式订单数据
- **请求体**: SapNewOrderRequest格式的JSON
- **响应**: SapApiResult格式

### 业务逻辑
1. 接收新格式的JSON数据(包含订单头和行项目信息)
2. 解析EKPO数组中的每个行项目
3. 对于每个行项目，处理其EKET交货计划数组
4. 如果有多个交货计划，为每个计划创建一个订单行项目
5. 转换为TblOrder对象，包含所有行项目
6. 根据订单编号检查是否已存在
7. 如果存在则更新，不存在则插入
8. 新插入的订单状态默认设置为"New"

## 数据库迁移

### 执行SQL脚本
```sql
-- 添加Status列
ALTER TABLE `tbl_order`
ADD COLUMN `Status` varchar(20) NOT NULL DEFAULT 'New' COMMENT '订单状态'
AFTER `Kafka_Status`;

-- 为现有数据设置默认状态
UPDATE `tbl_order` SET `Status` = 'New' WHERE `Status` IS NULL OR `Status` = '';

-- 添加索引
CREATE INDEX `idx_order_status` ON `tbl_order` (`Status`);
```

## 测试建议

### 1. 单元测试
- 测试SapObjectConverter.convertNewFormatToTblOrder()方法
- 测试新API接口的数据转换和存储

### 2. 集成测试
- 使用提供的JSON格式测试完整的接收流程
- 验证订单状态正确设置为"New"
- 测试重复订单的更新逻辑

### 3. 数据验证
- 确认数据库中Status字段正确添加
- 验证新订单状态为"New"
- 检查数据转换的准确性

## 注意事项

1. **向后兼容性**: 原有的`/api/order/sapPo`接口保持不变
2. **事务处理**: 新接口使用事务确保数据一致性
3. **错误处理**: 完善的异常处理和日志记录
4. **状态管理**: 新订单默认状态为"New"，可后续扩展状态流转逻辑

## 文件清单

### 修改的文件
- `datalink-data-manage/src/main/java/com/datalink/common/DataConstants.java`
- `datalink-data-manage/src/main/java/com/datalink/datamanage/domain/TblOrder.java`
- `datalink-data-manage/src/main/java/com/datalink/api/common/SapObjectConverter.java`
- `datalink-data-manage/src/main/java/com/datalink/api/controller/TblOrderApi.java`
- `datalink-data-manage/src/main/resources/mapper/datamanage/TblOrderMapper.xml`
- `sql/datalink.sql`

### 新增的文件
- `datalink-data-manage/src/main/java/com/datalink/api/domain/SapEketItem.java`
- `datalink-data-manage/src/main/java/com/datalink/api/domain/SapEkpoItem.java`
- `datalink-data-manage/src/main/java/com/datalink/api/domain/SapNewOrderRequest.java`
- `sql/add_order_status_column.sql`
- `test_new_order_data.json`
