# 批量接收订单接口调整说明

## 概述
根据提供的新JSON报文格式，对批量接收订单接口进行了调整，并新增了订单状态字段。

## 主要变更

### 1. 数据库结构变更
- **表名**: `tbl_order`
- **新增字段**: `Status` varchar(20) NOT NULL DEFAULT 'New' COMMENT '订单状态'
- **位置**: 在 `Kafka_Status` 字段之后
- **默认值**: 'New'

### 2. 新增常量定义
在 `DataConstants.java` 中新增：
```java
public static final String ORDER_STATUS_NEW = "New";
public static final String ORDER_STATUS_CONFIRMED = "Confirmed";
```

### 3. 实体类更新
- **TblOrder.java**: 新增 `status` 字段及其getter/setter方法

### 4. 新增DTO类
- **SapOrderItem.java**: 处理新JSON格式的订单项目数据
- **SapOrderRequest.java**: 处理新JSON格式的请求包装类

### 5. 转换器更新
- **SapObjectConverter.java**: 新增 `convertNewFormatToTblOrder()` 方法处理新格式数据转换

### 6. 数据库映射更新
- **TblOrderMapper.xml**: 更新所有相关SQL语句以支持Status字段

### 7. 新增API接口
- **TblOrderApi.java**: 新增 `/api/order/batchReceiveOrders` 接口

## 新JSON格式说明

### 请求格式
```json
{
  "DATA": [
    {
      "ZFNO": "0000000030",      // 订单编号
      "ZFPOS": "000001",         // 订单行项目编号
      "WERKS": "2171",           // 工厂代码
      "MATNR": "SD_TEST001",     // 物料编号
      "LIFNR": "0000400006",     // 供应商编号
      "FCTYPE": "M",             // 预测类型
      "KMONTH": "20211101",      // 月份(YYYYMMDD格式)
      "MENGE": "1000",           // 数量
      "MEINS": "PCS"             // 单位
    }
  ]
}
```

### 字段映射关系
| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| ZFNO | Order_Code | 订单编号 |
| ZFPOS | Item_No | 行项目编号 |
| WERKS | Plant_Code | 工厂代码 |
| MATNR | Article_No | 物料编号 |
| LIFNR | Supp_Code | 供应商编号 |
| FCTYPE | Item_Type | 项目类别 |
| KMONTH | Delivery_Date | 交货日期 |
| MENGE | Quantity | 数量 |
| MEINS | Unit | 单位 |

## API接口说明

### 新接口
- **URL**: `POST /api/order/batchReceiveOrders`
- **功能**: 批量接收新格式订单数据
- **请求体**: SapOrderRequest格式的JSON
- **响应**: SapApiResult格式

### 业务逻辑
1. 接收新格式的JSON数据
2. 转换为TblOrder对象
3. 根据订单编号检查是否已存在
4. 如果存在则更新，不存在则插入
5. 新插入的订单状态默认设置为"New"

## 数据库迁移

### 执行SQL脚本
```sql
-- 添加Status列
ALTER TABLE `tbl_order` 
ADD COLUMN `Status` varchar(20) NOT NULL DEFAULT 'New' COMMENT '订单状态' 
AFTER `Kafka_Status`;

-- 为现有数据设置默认状态
UPDATE `tbl_order` SET `Status` = 'New' WHERE `Status` IS NULL OR `Status` = '';

-- 添加索引
CREATE INDEX `idx_order_status` ON `tbl_order` (`Status`);
```

## 测试建议

### 1. 单元测试
- 测试SapObjectConverter.convertNewFormatToTblOrder()方法
- 测试新API接口的数据转换和存储

### 2. 集成测试
- 使用提供的JSON格式测试完整的接收流程
- 验证订单状态正确设置为"New"
- 测试重复订单的更新逻辑

### 3. 数据验证
- 确认数据库中Status字段正确添加
- 验证新订单状态为"New"
- 检查数据转换的准确性

## 注意事项

1. **向后兼容性**: 原有的`/api/order/sapPo`接口保持不变
2. **事务处理**: 新接口使用事务确保数据一致性
3. **错误处理**: 完善的异常处理和日志记录
4. **状态管理**: 新订单默认状态为"New"，可后续扩展状态流转逻辑

## 文件清单

### 修改的文件
- `datalink-data-manage/src/main/java/com/datalink/common/DataConstants.java`
- `datalink-data-manage/src/main/java/com/datalink/datamanage/domain/TblOrder.java`
- `datalink-data-manage/src/main/java/com/datalink/api/common/SapObjectConverter.java`
- `datalink-data-manage/src/main/java/com/datalink/api/controller/TblOrderApi.java`
- `datalink-data-manage/src/main/resources/mapper/datamanage/TblOrderMapper.xml`
- `sql/datalink.sql`

### 新增的文件
- `datalink-data-manage/src/main/java/com/datalink/api/domain/SapOrderItem.java`
- `datalink-data-manage/src/main/java/com/datalink/api/domain/SapOrderRequest.java`
- `sql/add_order_status_column.sql`
